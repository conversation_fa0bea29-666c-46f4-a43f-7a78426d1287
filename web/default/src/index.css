/* CSS Variables for Theme Support */
:root {
    /* Light theme colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-tertiary: #e9ecef;
    --text-primary: #212529;
    --text-secondary: #6c757d;
    --text-muted: #adb5bd;
    --border-color: #dee2e6;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --menu-bg: #ffffff;
    --menu-hover: #f8f9fa;
    --menu-active: #e9ecef;
    --card-bg: #ffffff;
    --input-bg: #ffffff;
    --input-border: #ced4da;
    --button-primary: #007bff;
    --button-secondary: #6c757d;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --error-color: #dc3545;
    --info-color: #17a2b8;
}

[data-theme="dark"] {
    /* Dark theme colors - More refined palette */
    --bg-primary: #1e1e1e;
    --bg-secondary: #2a2a2a;
    --bg-tertiary: #363636;
    --text-primary: #e4e4e4;
    --text-secondary: #b8b8b8;
    --text-muted: #888888;
    --border-color: #3a3a3a;
    --shadow-color: rgba(0, 0, 0, 0.4);
    --menu-bg: #2a2a2a;
    --menu-hover: #363636;
    --menu-active: #404040;
    --card-bg: #2a2a2a;
    --input-bg: #363636;
    --input-border: #4a4a4a;
    --button-primary: #4a9eff;
    --button-secondary: #5a5a5a;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --error-color: #f44336;
    --info-color: #2196f3;
}

body {
    margin: 0;
    padding-top: 60px;
    /* Increased from 55px to account for header height */
    overflow-y: scroll;
    font-family: Lato, 'Helvetica Neue', Arial, Helvetica, "Microsoft YaHei", sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    scrollbar-width: none;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    transition: background-color 0.3s ease, color 0.3s ease;
    min-height: 100vh;
}

html {
    height: 100%;
}

#root {
    min-height: calc(100vh - 60px);
    /* Updated to match body padding-top */
    display: flex;
    flex-direction: column;
    background-color: var(--bg-primary);
}

body::-webkit-scrollbar {
    display: none;
}

code {
    font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New', monospace;
}

.main-content {
    padding: 8px;
    /* Increased from 4px for better spacing */
    background-color: var(--bg-primary);
    flex: 1;
    min-height: calc(100vh - 60px - 60px);
    /* Updated header height */
}

/* Footer positioning */
.ui.vertical.segment {
    background-color: var(--bg-secondary) !important;
    margin-top: auto !important;
    padding: 1rem 0 !important;
}

[data-theme="dark"] .ui.vertical.segment {
    background-color: var(--bg-secondary) !important;
    border-top: 1px solid var(--border-color) !important;
}

/* Dashboard and settings container */
.dashboard-container {
    background-color: var(--bg-primary) !important;
    min-height: calc(100vh - 60px - 60px);
    /* Updated header height */
    padding: 1rem;
}

[data-theme="dark"] .dashboard-container {
    background-color: var(--bg-primary) !important;
}

/* Settings page specific styling */
[data-theme="dark"] .chart-card {
    background-color: var(--card-bg) !important;
    border: 1px solid var(--border-color) !important;
    box-shadow: 0 2px 4px var(--shadow-color) !important;
}

[data-theme="dark"] .chart-card .content {
    background-color: var(--card-bg) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .chart-card .header {
    color: var(--text-primary) !important;
    background-color: var(--card-bg) !important;
}

/* Settings tabs styling */
[data-theme="dark"] .settings-tab .item {
    color: var(--text-primary) !important;
    background-color: var(--bg-secondary) !important;
    border-color: var(--border-color) !important;
}

[data-theme="dark"] .settings-tab .active.item {
    color: var(--text-primary) !important;
    background-color: var(--card-bg) !important;
    border-color: var(--button-primary) !important;
    border-bottom-color: var(--card-bg) !important;
}

[data-theme="dark"] .settings-tab .item:hover {
    background-color: var(--menu-hover) !important;
    color: var(--text-primary) !important;
}

/* Ensure all page backgrounds are properly themed */
[data-theme="dark"] .ui.container {
    background-color: transparent !important;
}

[data-theme="dark"] .ui.grid {
    background-color: transparent !important;
}

[data-theme="dark"] .ui.grid>.row {
    background-color: transparent !important;
}

[data-theme="dark"] .ui.grid>.column {
    background-color: transparent !important;
}

/* Fix any remaining white backgrounds */
[data-theme="dark"] * {
    background-color: inherit;
}

[data-theme="dark"] .ui.card,
[data-theme="dark"] .ui.cards>.card {
    background-color: var(--card-bg) !important;
    border: 1px solid var(--border-color) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .ui.card>.content,
[data-theme="dark"] .ui.cards>.card>.content {
    background-color: var(--card-bg) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
}

/* Footer text color fix */
[data-theme="dark"] .custom-footer {
    color: var(--text-secondary) !important;
}

[data-theme="dark"] .custom-footer a {
    color: var(--button-primary) !important;
}

[data-theme="dark"] .custom-footer a:hover {
    color: var(--text-primary) !important;
}

/* Theme-aware Semantic UI overrides */
[data-theme="dark"] .ui.menu {
    background-color: var(--menu-bg) !important;
    border-color: var(--border-color) !important;
    box-shadow: 0 1px 3px var(--shadow-color) !important;
}

[data-theme="dark"] .ui.menu .item {
    color: var(--text-secondary) !important;
    transition: all 0.2s ease !important;
}

[data-theme="dark"] .ui.menu .item:hover {
    background-color: var(--menu-hover) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .ui.menu .active.item {
    background-color: var(--menu-active) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .ui.container {
    background-color: transparent !important;
}

[data-theme="dark"] .ui.segment {
    background-color: var(--card-bg) !important;
    border: 1px solid var(--border-color) !important;
    color: var(--text-primary) !important;
    box-shadow: 0 2px 8px var(--shadow-color) !important;
}

[data-theme="dark"] .ui.card {
    background-color: var(--card-bg) !important;
    border: 1px solid var(--border-color) !important;
    color: var(--text-primary) !important;
    box-shadow: 0 2px 8px var(--shadow-color) !important;
}

[data-theme="dark"] .ui.table {
    background-color: var(--card-bg) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--border-color) !important;
}

[data-theme="dark"] .ui.table thead th {
    background-color: var(--bg-secondary) !important;
    color: var(--text-primary) !important;
    border-bottom: 1px solid var(--border-color) !important;
}

[data-theme="dark"] .ui.table tbody tr {
    border-bottom: 1px solid var(--border-color) !important;
}

[data-theme="dark"] .ui.table tbody tr:hover {
    background-color: var(--menu-hover) !important;
}

[data-theme="dark"] .ui.table tbody td {
    border-top: 1px solid var(--border-color) !important;
}

[data-theme="dark"] .ui.input input {
    background-color: var(--input-bg) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--input-border) !important;
}

[data-theme="dark"] .ui.input input:focus {
    border-color: var(--button-primary) !important;
    background-color: var(--input-bg) !important;
}

/* Text areas and form inputs */
[data-theme="dark"] textarea {
    background-color: var(--input-bg) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--input-border) !important;
}

[data-theme="dark"] textarea:focus {
    border-color: var(--button-primary) !important;
    background-color: var(--input-bg) !important;
    outline: none !important;
}

[data-theme="dark"] .ui.form textarea {
    background-color: var(--input-bg) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--input-border) !important;
}

[data-theme="dark"] .ui.form textarea:focus {
    border-color: var(--button-primary) !important;
    background-color: var(--input-bg) !important;
}

[data-theme="dark"] .ui.form input[type="text"],
[data-theme="dark"] .ui.form input[type="email"],
[data-theme="dark"] .ui.form input[type="password"],
[data-theme="dark"] .ui.form input[type="number"],
[data-theme="dark"] .ui.form input[type="url"] {
    background-color: var(--input-bg) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--input-border) !important;
}

[data-theme="dark"] .ui.form input[type="text"]:focus,
[data-theme="dark"] .ui.form input[type="email"]:focus,
[data-theme="dark"] .ui.form input[type="password"]:focus,
[data-theme="dark"] .ui.form input[type="number"]:focus,
[data-theme="dark"] .ui.form input[type="url"]:focus {
    border-color: var(--button-primary) !important;
    background-color: var(--input-bg) !important;
}

[data-theme="dark"] .ui.dropdown {
    background-color: var(--input-bg) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--input-border) !important;
}

[data-theme="dark"] .ui.dropdown .menu {
    background-color: var(--menu-bg) !important;
    border: 1px solid var(--border-color) !important;
    box-shadow: 0 4px 12px var(--shadow-color) !important;
}

[data-theme="dark"] .ui.dropdown .menu .item {
    color: var(--text-primary) !important;
    border: none !important;
}

[data-theme="dark"] .ui.dropdown .menu .item:hover {
    background-color: var(--menu-hover) !important;
}

[data-theme="dark"] .ui.dropdown .text {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .ui.button {
    background-color: var(--bg-secondary) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--border-color) !important;
    transition: all 0.2s ease !important;
}

[data-theme="dark"] .ui.button:hover {
    background-color: var(--bg-tertiary) !important;
}

[data-theme="dark"] .ui.button.primary {
    background-color: var(--button-primary) !important;
    color: white !important;
    border-color: var(--button-primary) !important;
}

[data-theme="dark"] .ui.button.primary:hover {
    background-color: rgba(74, 158, 255, 0.8) !important;
}

[data-theme="dark"] .ui.button.green {
    background-color: var(--success-color) !important;
    color: white !important;
}

[data-theme="dark"] .ui.button.red {
    background-color: var(--error-color) !important;
    color: white !important;
}

[data-theme="dark"] .ui.button.orange {
    background-color: var(--warning-color) !important;
    color: white !important;
}

[data-theme="dark"] .ui.modal {
    background-color: var(--card-bg) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .ui.modal .header {
    background-color: var(--bg-secondary) !important;
    color: var(--text-primary) !important;
    border-bottom: 1px solid var(--border-color) !important;
}

[data-theme="dark"] .ui.modal .content {
    background-color: var(--card-bg) !important;
    color: var(--text-primary) !important;
}

/* Dark mode label styles - comprehensive coverage for all colors */
[data-theme="dark"] .ui.label {
    background-color: var(--bg-secondary) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--border-color) !important;
}

/* Basic labels with dark-friendly colors */
[data-theme="dark"] .ui.label.basic {
    background-color: rgba(255, 255, 255, 0.08) !important;
    color: var(--text-primary) !important;
    border: 1px solid rgba(255, 255, 255, 0.15) !important;
}

/* Color-specific label styles for dark mode */
[data-theme="dark"] .ui.label.red,
[data-theme="dark"] .ui.label.basic.red {
    background-color: rgba(244, 67, 54, 0.15) !important;
    color: #ff8a80 !important;
    border: 1px solid rgba(244, 67, 54, 0.3) !important;
}

[data-theme="dark"] .ui.label.orange,
[data-theme="dark"] .ui.label.basic.orange {
    background-color: rgba(255, 152, 0, 0.15) !important;
    color: #ffb74d !important;
    border: 1px solid rgba(255, 152, 0, 0.3) !important;
}

[data-theme="dark"] .ui.label.yellow,
[data-theme="dark"] .ui.label.basic.yellow {
    background-color: rgba(255, 235, 59, 0.15) !important;
    color: #fff176 !important;
    border: 1px solid rgba(255, 235, 59, 0.3) !important;
}

[data-theme="dark"] .ui.label.olive,
[data-theme="dark"] .ui.label.basic.olive {
    background-color: rgba(139, 195, 74, 0.15) !important;
    color: #aed581 !important;
    border: 1px solid rgba(139, 195, 74, 0.3) !important;
}

[data-theme="dark"] .ui.label.green,
[data-theme="dark"] .ui.label.basic.green {
    background-color: rgba(76, 175, 80, 0.15) !important;
    color: #81c784 !important;
    border: 1px solid rgba(76, 175, 80, 0.3) !important;
}

[data-theme="dark"] .ui.label.teal,
[data-theme="dark"] .ui.label.basic.teal {
    background-color: rgba(0, 150, 136, 0.15) !important;
    color: #4db6ac !important;
    border: 1px solid rgba(0, 150, 136, 0.3) !important;
}

[data-theme="dark"] .ui.label.blue,
[data-theme="dark"] .ui.label.basic.blue {
    background-color: rgba(33, 150, 243, 0.15) !important;
    color: #64b5f6 !important;
    border: 1px solid rgba(33, 150, 243, 0.3) !important;
}

[data-theme="dark"] .ui.label.violet,
[data-theme="dark"] .ui.label.basic.violet {
    background-color: rgba(156, 39, 176, 0.15) !important;
    color: #ba68c8 !important;
    border: 1px solid rgba(156, 39, 176, 0.3) !important;
}

[data-theme="dark"] .ui.label.purple,
[data-theme="dark"] .ui.label.basic.purple {
    background-color: rgba(103, 58, 183, 0.15) !important;
    color: #9575cd !important;
    border: 1px solid rgba(103, 58, 183, 0.3) !important;
}

[data-theme="dark"] .ui.label.pink,
[data-theme="dark"] .ui.label.basic.pink {
    background-color: rgba(233, 30, 99, 0.15) !important;
    color: #f06292 !important;
    border: 1px solid rgba(233, 30, 99, 0.3) !important;
}

[data-theme="dark"] .ui.label.brown,
[data-theme="dark"] .ui.label.basic.brown {
    background-color: rgba(121, 85, 72, 0.15) !important;
    color: #a1887f !important;
    border: 1px solid rgba(121, 85, 72, 0.3) !important;
}

[data-theme="dark"] .ui.label.grey,
[data-theme="dark"] .ui.label.basic.grey {
    background-color: rgba(158, 158, 158, 0.15) !important;
    color: #bdbdbd !important;
    border: 1px solid rgba(158, 158, 158, 0.3) !important;
}

[data-theme="dark"] .ui.label.black,
[data-theme="dark"] .ui.label.basic.black {
    background-color: rgba(97, 97, 97, 0.15) !important;
    color: #9e9e9e !important;
    border: 1px solid rgba(97, 97, 97, 0.3) !important;
}

/* Theme toggle button styles */
.theme-toggle-button {
    background: none !important;
    border: none !important;
    padding: 8px !important;
    margin: 0 8px !important;
    cursor: pointer;
    border-radius: 6px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.theme-toggle-button:hover {
    background-color: var(--menu-hover) !important;
    transform: scale(1.05);
}

.theme-toggle-button .icon {
    font-size: 18px !important;
    color: var(--text-secondary) !important;
    margin: 0 !important;
    transition: color 0.2s ease;
}

.theme-toggle-button:hover .icon {
    color: var(--text-primary) !important;
}

/* Additional dark theme improvements */
[data-theme="dark"] .ui.search .results {
    background-color: var(--menu-bg) !important;
    border: 1px solid var(--border-color) !important;
    box-shadow: 0 4px 12px var(--shadow-color) !important;
}

[data-theme="dark"] .ui.search .result {
    background-color: var(--menu-bg) !important;
    color: var(--text-primary) !important;
    border-bottom: 1px solid var(--border-color) !important;
}

[data-theme="dark"] .ui.search .result:hover {
    background-color: var(--menu-hover) !important;
}

[data-theme="dark"] .ui.form .field>label {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .ui.checkbox label {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .ui.checkbox input:checked~.box:before,
[data-theme="dark"] .ui.checkbox input:checked~label:before {
    background-color: var(--button-primary) !important;
    border-color: var(--button-primary) !important;
}

[data-theme="dark"] .ui.pagination .item {
    background-color: var(--bg-secondary) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--border-color) !important;
}

[data-theme="dark"] .ui.pagination .active.item {
    background-color: var(--button-primary) !important;
    color: white !important;
}

[data-theme="dark"] .ui.pagination .item:hover {
    background-color: var(--menu-hover) !important;
}

/* Toast notifications dark theme */
[data-theme="dark"] .Toastify__toast {
    background-color: var(--card-bg) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--border-color) !important;
}

[data-theme="dark"] .Toastify__toast--success {
    background-color: var(--success-color) !important;
}

[data-theme="dark"] .Toastify__toast--error {
    background-color: var(--error-color) !important;
}

[data-theme="dark"] .Toastify__toast--warning {
    background-color: var(--warning-color) !important;
}

[data-theme="dark"] .Toastify__toast--info {
    background-color: var(--info-color) !important;
}

/* Code editors and JSON formatters */
[data-theme="dark"] .ui.form .field textarea,
[data-theme="dark"] .ui.form .field input {
    background-color: var(--input-bg) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--input-border) !important;
}

[data-theme="dark"] .ui.form .field textarea:focus,
[data-theme="dark"] .ui.form .field input:focus {
    border-color: var(--button-primary) !important;
    background-color: var(--input-bg) !important;
}

/* Code blocks and pre elements */
[data-theme="dark"] pre {
    background-color: var(--bg-secondary) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--border-color) !important;
}

[data-theme="dark"] code {
    background-color: var(--bg-secondary) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--border-color) !important;
}

/* JSON editor specific */
[data-theme="dark"] .json-editor,
[data-theme="dark"] .json-formatter {
    background-color: var(--input-bg) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--input-border) !important;
}

/* Generic white backgrounds that might appear */
[data-theme="dark"] .ui.form .field>.ui.input,
[data-theme="dark"] .ui.form .field>.ui.dropdown {
    background-color: var(--input-bg) !important;
}

[data-theme="dark"] .ui.form .field>.ui.input input {
    background-color: var(--input-bg) !important;
    color: var(--text-primary) !important;
}

/* Tab content areas */
[data-theme="dark"] .ui.tab.segment {
    background-color: var(--card-bg) !important;
    border: 1px solid var(--border-color) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .ui.tabular.menu .item {
    background-color: var(--bg-secondary) !important;
    color: var(--text-secondary) !important;
    border: 1px solid var(--border-color) !important;
}

[data-theme="dark"] .ui.tabular.menu .active.item {
    background-color: var(--card-bg) !important;
    color: var(--text-primary) !important;
    border-bottom-color: var(--card-bg) !important;
}

/* Message boxes and info panels */
[data-theme="dark"] .ui.message {
    background-color: var(--bg-secondary) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--border-color) !important;
}

[data-theme="dark"] .ui.message.info {
    background-color: rgba(59, 130, 246, 0.1) !important;
    border-color: var(--info-color) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .ui.message.warning {
    background-color: rgba(245, 158, 11, 0.1) !important;
    border-color: var(--warning-color) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .ui.message.error {
    background-color: rgba(239, 68, 68, 0.1) !important;
    border-color: var(--error-color) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .ui.message.success {
    background-color: rgba(34, 197, 94, 0.1) !important;
    border-color: var(--success-color) !important;
    color: var(--text-primary) !important;
}

/* Additional form elements and containers */
[data-theme="dark"] .ui.form .field {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .ui.form .field .ui.selection.dropdown {
    background-color: var(--input-bg) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--input-border) !important;
}

[data-theme="dark"] .ui.form .field .ui.selection.dropdown .text {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .ui.form .field .ui.selection.dropdown .menu {
    background-color: var(--menu-bg) !important;
    border: 1px solid var(--border-color) !important;
}

[data-theme="dark"] .ui.form .field .ui.selection.dropdown .menu .item {
    background-color: var(--menu-bg) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .ui.form .field .ui.selection.dropdown .menu .item:hover {
    background-color: var(--menu-hover) !important;
}

/* Content areas and panels */
[data-theme="dark"] .ui.grid>.column {
    background-color: transparent !important;
}

[data-theme="dark"] .ui.container>.ui.grid {
    background-color: transparent !important;
}

/* Specific overrides for any remaining white backgrounds */
[data-theme="dark"] * {
    scrollbar-width: thin;
    scrollbar-color: var(--border-color) var(--bg-secondary);
}

[data-theme="dark"] *::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

[data-theme="dark"] *::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

[data-theme="dark"] *::-webkit-scrollbar-thumb {
    background-color: var(--border-color);
    border-radius: 4px;
}

[data-theme="dark"] *::-webkit-scrollbar-thumb:hover {
    background-color: var(--text-secondary);
}

/* Ensure all text inputs have dark styling */
[data-theme="dark"] input,
[data-theme="dark"] textarea,
[data-theme="dark"] select {
    background-color: var(--input-bg) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--input-border) !important;
}

[data-theme="dark"] input:focus,
[data-theme="dark"] textarea:focus,
[data-theme="dark"] select:focus {
    border-color: var(--button-primary) !important;
    background-color: var(--input-bg) !important;
    outline: none !important;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
}

/* Logo and header text styling for dark mode */
[data-theme="dark"] .ui.menu .item div {
    color: var(--text-primary) !important;
}

/* Homepage content text styling */
[data-theme="dark"] .ui.header {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .ui.card .header {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .ui.card .description {
    color: var(--text-secondary) !important;
}

/* Ensure all paragraph text is visible in dark mode */
[data-theme="dark"] p {
    color: var(--text-primary) !important;
}

[data-theme="dark"] span {
    color: inherit !important;
}

/* Links in dark mode */
[data-theme="dark"] a {
    color: var(--button-primary) !important;
}

[data-theme="dark"] a:hover {
    color: var(--text-primary) !important;
}

/* Chart and tooltip styling for dark mode */
[data-theme="dark"] .recharts-tooltip-wrapper {
    background-color: var(--card-bg) !important;
    border: 1px solid var(--border-color) !important;
    border-radius: 4px !important;
    box-shadow: 0 2px 8px var(--shadow-color) !important;
}

[data-theme="dark"] .recharts-default-tooltip {
    background-color: var(--card-bg) !important;
    border: 1px solid var(--border-color) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .recharts-tooltip-label {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .recharts-tooltip-item {
    color: var(--text-primary) !important;
}

/* Dashboard specific styling */
[data-theme="dark"] .dashboard-container .stat-value {
    background: rgba(74, 158, 255, 0.15) !important;
    color: var(--button-primary) !important;
}

/* Ensure chart backgrounds are properly themed */
[data-theme="dark"] .recharts-surface {
    background-color: transparent !important;
}

[data-theme="dark"] .recharts-cartesian-grid line {
    stroke: var(--border-color) !important;
    opacity: 0.3 !important;
}

[data-theme="dark"] .recharts-text {
    fill: var(--text-secondary) !important;
}

/* Theme dropdown styling - Enhanced for better visibility with higher specificity */
.ui.menu .theme-dropdown.ui.dropdown .menu,
.theme-dropdown.ui.dropdown .menu {
    min-width: 140px !important;
    border: 1px solid var(--border-color) !important;
    box-shadow: 0 4px 12px var(--shadow-color) !important;
    background-color: var(--card-bg) !important;
}

[data-theme="dark"] .ui.menu .theme-dropdown.ui.dropdown .menu,
[data-theme="dark"] .theme-dropdown.ui.dropdown .menu {
    background-color: var(--card-bg) !important;
    border: 1px solid var(--border-color) !important;
}

[data-theme="dark"] .ui.menu .theme-dropdown.ui.dropdown .menu .item,
[data-theme="dark"] .theme-dropdown.ui.dropdown .menu .item {
    color: var(--text-primary) !important;
    background-color: var(--card-bg) !important;
    border-bottom: 1px solid var(--border-color) !important;
    padding: 12px 16px !important;
    transition: all 0.2s ease !important;
}

[data-theme="dark"] .ui.menu .theme-dropdown.ui.dropdown .menu .item:last-child,
[data-theme="dark"] .theme-dropdown.ui.dropdown .menu .item:last-child {
    border-bottom: none !important;
}

[data-theme="dark"] .ui.menu .theme-dropdown.ui.dropdown .menu .item:hover,
[data-theme="dark"] .theme-dropdown.ui.dropdown .menu .item:hover {
    background-color: var(--menu-hover) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .ui.menu .theme-dropdown.ui.dropdown .menu .item.active,
[data-theme="dark"] .theme-dropdown.ui.dropdown .menu .item.active {
    background-color: var(--button-primary) !important;
    color: white !important;
    font-weight: 600 !important;
}

[data-theme="dark"] .ui.menu .theme-dropdown.ui.dropdown .menu .item.active:hover,
[data-theme="dark"] .theme-dropdown.ui.dropdown .menu .item.active:hover {
    background-color: rgba(74, 158, 255, 0.8) !important;
    color: white !important;
}

/* Light mode theme dropdown styling for better contrast */
.ui.menu .theme-dropdown.ui.dropdown .menu .item,
.theme-dropdown.ui.dropdown .menu .item {
    color: var(--text-primary) !important;
    background-color: var(--card-bg) !important;
    border-bottom: 1px solid var(--border-color) !important;
    padding: 12px 16px !important;
    transition: all 0.2s ease !important;
}

.ui.menu .theme-dropdown.ui.dropdown .menu .item:last-child,
.theme-dropdown.ui.dropdown .menu .item:last-child {
    border-bottom: none !important;
}

.ui.menu .theme-dropdown.ui.dropdown .menu .item:hover,
.theme-dropdown.ui.dropdown .menu .item:hover {
    background-color: var(--menu-hover) !important;
    color: var(--text-primary) !important;
}

.ui.menu .theme-dropdown.ui.dropdown .menu .item.active,
.theme-dropdown.ui.dropdown .menu .item.active {
    background-color: var(--button-primary) !important;
    color: white !important;
    font-weight: 600 !important;
}

.ui.menu .theme-dropdown.ui.dropdown .menu .item.active:hover,
.theme-dropdown.ui.dropdown .menu .item.active:hover {
    background-color: rgba(0, 123, 255, 0.8) !important;
    color: white !important;
}

/* Ensure theme dropdown trigger is properly styled */
[data-theme="dark"] .ui.dropdown>.dropdown.icon {
    color: var(--text-secondary) !important;
}

[data-theme="dark"] .ui.dropdown>.text {
    color: var(--text-secondary) !important;
}

/* Additional fallback styling for theme dropdown in dark mode */
[data-theme="dark"] .ui.dropdown.theme-dropdown .menu .item,
[data-theme="dark"] .ui.menu .ui.dropdown.theme-dropdown .menu .item {
    background-color: var(--card-bg) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
}

[data-theme="dark"] .ui.dropdown.theme-dropdown .menu .item:hover,
[data-theme="dark"] .ui.menu .ui.dropdown.theme-dropdown .menu .item:hover {
    background-color: var(--menu-hover) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .ui.dropdown.theme-dropdown .menu .item.active,
[data-theme="dark"] .ui.menu .ui.dropdown.theme-dropdown .menu .item.active {
    background-color: var(--button-primary) !important;
    color: white !important;
}

/* Force override for Semantic UI dropdown menu in dark mode */
[data-theme="dark"] .ui.dropdown .menu {
    background-color: var(--card-bg) !important;
    border: 1px solid var(--border-color) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .ui.dropdown .menu .item {
    background-color: var(--card-bg) !important;
    color: var(--text-primary) !important;
    border-bottom: 1px solid var(--border-color) !important;
}

[data-theme="dark"] .ui.dropdown .menu .item:hover {
    background-color: var(--menu-hover) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .ui.dropdown .menu .item.active {
    background-color: var(--button-primary) !important;
    color: white !important;
}

/* Better styling for all dropdown menus in dark mode */
[data-theme="dark"] .ui.dropdown .menu .item {
    color: var(--text-primary) !important;
    background-color: var(--menu-bg) !important;
}

[data-theme="dark"] .ui.dropdown .menu .item:hover {
    background-color: var(--menu-hover) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .ui.dropdown .menu .item.active {
    background-color: var(--button-primary) !important;
    color: white !important;
}

.small-icon .icon {
    font-size: 1em !important;
}

.custom-footer {
    font-size: 1.1em;
}

@media only screen and (max-width: 600px) {
    .hide-on-mobile {
        display: none !important;
    }
}

/* Progressive header disclosure styles */
.header-more-dropdown .dropdown.menu {
    min-width: 180px !important;
    background-color: var(--card-bg) !important;
    border: 1px solid var(--border-color) !important;
    box-shadow: 0 4px 12px var(--shadow-color) !important;
}

.header-more-dropdown .dropdown.item {
    padding: 8px 16px !important;
    background-color: var(--card-bg) !important;
    color: var(--text-primary) !important;
    border-bottom: 1px solid var(--border-color) !important;
    transition: all 0.2s ease !important;
}

.header-more-dropdown .dropdown.item:hover {
    background-color: var(--menu-hover) !important;
    color: var(--text-primary) !important;
}

.header-more-dropdown .dropdown.item:last-child {
    border-bottom: none !important;
}

/* Dark theme support for header more dropdown */
[data-theme="dark"] .header-more-dropdown .dropdown.menu {
    background-color: var(--card-bg) !important;
    border: 1px solid var(--border-color) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
}

[data-theme="dark"] .header-more-dropdown .dropdown.item {
    background-color: var(--card-bg) !important;
    color: var(--text-primary) !important;
    border-bottom: 1px solid var(--border-color) !important;
}

[data-theme="dark"] .header-more-dropdown .dropdown.item:hover {
    background-color: var(--menu-hover) !important;
    color: var(--text-primary) !important;
}

/* ==============================|| LOG LABELS DARK MODE FIX ||============================== */

/* Fix bright colored labels in logs for better dark mode readability */
[data-theme="dark"] .ui.label {
    background-color: rgba(255, 255, 255, 0.1) !important;
    color: var(--text-primary) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

/* Specific color adjustments for log type labels in dark mode */
[data-theme="dark"] .ui.label.green {
    background-color: rgba(33, 186, 69, 0.2) !important;
    color: #4ade80 !important;
    border: 1px solid rgba(33, 186, 69, 0.3) !important;
}

[data-theme="dark"] .ui.label.olive {
    background-color: rgba(181, 204, 24, 0.2) !important;
    color: #a3e635 !important;
    border: 1px solid rgba(181, 204, 24, 0.3) !important;
}

[data-theme="dark"] .ui.label.orange {
    background-color: rgba(242, 113, 28, 0.2) !important;
    color: #fb923c !important;
    border: 1px solid rgba(242, 113, 28, 0.3) !important;
}

[data-theme="dark"] .ui.label.purple {
    background-color: rgba(163, 73, 164, 0.2) !important;
    color: #c084fc !important;
    border: 1px solid rgba(163, 73, 164, 0.3) !important;
}

[data-theme="dark"] .ui.label.violet {
    background-color: rgba(139, 69, 195, 0.2) !important;
    color: #a855f7 !important;
    border: 1px solid rgba(139, 69, 195, 0.3) !important;
}

[data-theme="dark"] .ui.label.pink {
    background-color: rgba(224, 102, 102, 0.2) !important;
    color: #f472b6 !important;
    border: 1px solid rgba(224, 102, 102, 0.3) !important;
}

[data-theme="dark"] .ui.label.red {
    background-color: rgba(219, 40, 40, 0.2) !important;
    color: #ef4444 !important;
    border: 1px solid rgba(219, 40, 40, 0.3) !important;
}

[data-theme="dark"] .ui.label.black {
    background-color: rgba(128, 128, 128, 0.2) !important;
    color: #9ca3af !important;
    border: 1px solid rgba(128, 128, 128, 0.3) !important;
}

/* Ensure basic labels also follow dark theme */
[data-theme="dark"] .ui.basic.label {
    background-color: rgba(255, 255, 255, 0.05) !important;
    color: var(--text-primary) !important;
    border: 1px solid rgba(255, 255, 255, 0.15) !important;
}

[data-theme="dark"] .ui.basic.label.green {
    color: #4ade80 !important;
    border: 1px solid rgba(33, 186, 69, 0.4) !important;
}

[data-theme="dark"] .ui.basic.label.olive {
    color: #a3e635 !important;
    border: 1px solid rgba(181, 204, 24, 0.4) !important;
}

[data-theme="dark"] .ui.basic.label.orange {
    color: #fb923c !important;
    border: 1px solid rgba(242, 113, 28, 0.4) !important;
}

[data-theme="dark"] .ui.basic.label.purple {
    color: #c084fc !important;
    border: 1px solid rgba(163, 73, 164, 0.4) !important;
}

[data-theme="dark"] .ui.basic.label.violet {
    color: #a855f7 !important;
    border: 1px solid rgba(139, 69, 195, 0.4) !important;
}

[data-theme="dark"] .ui.basic.label.pink {
    color: #f472b6 !important;
    border: 1px solid rgba(224, 102, 102, 0.4) !important;
}

[data-theme="dark"] .ui.basic.label.red {
    color: #ef4444 !important;
    border: 1px solid rgba(219, 40, 40, 0.4) !important;
}

[data-theme="dark"] .ui.basic.label.black {
    color: #9ca3af !important;
    border: 1px solid rgba(128, 128, 128, 0.4) !important;
}

/* ==============================|| LOG PAGE BACKGROUND FIX ||============================== */

/* Fix bright white background in log content area for dark mode */
[data-theme="dark"] .ui.card {
    background-color: var(--card-bg) !important;
    border: 1px solid var(--border-color) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
}

[data-theme="dark"] .ui.card > .content {
    background-color: var(--card-bg) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .ui.card > .content > .header {
    color: var(--text-primary) !important;
}

/* Fix table background in dark mode */
[data-theme="dark"] .ui.table {
    background-color: var(--card-bg) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--border-color) !important;
}

[data-theme="dark"] .ui.table thead th {
    background-color: var(--card-bg) !important;
    color: var(--text-primary) !important;
    border-bottom: 1px solid var(--border-color) !important;
}

[data-theme="dark"] .ui.table tbody tr {
    background-color: var(--card-bg) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .ui.table tbody tr:hover {
    background-color: var(--menu-hover) !important;
}

[data-theme="dark"] .ui.table tbody td {
    border-top: 1px solid var(--border-color) !important;
    color: var(--text-primary) !important;
}

/* Fix form backgrounds in dark mode */
[data-theme="dark"] .ui.form {
    background-color: var(--card-bg) !important;
}

[data-theme="dark"] .ui.form .field > label {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .ui.form input:not([type]),
[data-theme="dark"] .ui.form input[type="text"],
[data-theme="dark"] .ui.form input[type="email"],
[data-theme="dark"] .ui.form input[type="search"],
[data-theme="dark"] .ui.form input[type="password"] {
    background-color: var(--input-bg) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--border-color) !important;
}

[data-theme="dark"] .ui.form .ui.dropdown {
    background-color: var(--input-bg) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--border-color) !important;
}

[data-theme="dark"] .ui.form .ui.dropdown > .text {
    color: var(--text-primary) !important;
}

/* ==============================|| MOBILE LOG CARDS STYLING ||============================== */

/* Mobile log card container */
.mobile-log-container {
    padding: 0 !important;
    margin: 0 !important;
    margin-top: 0 !important;
    padding-top: 0 !important;
    background-color: var(--bg-primary) !important;
    width: 100% !important;
    box-sizing: border-box !important;
    position: relative !important;
    top: 0 !important;
}

/* Individual mobile log card */
.mobile-log-card {
    background-color: var(--card-bg) !important;
    border-radius: 0px !important;
    margin: 2px 0px !important;
    padding: 12px 8px !important;
    box-shadow: none !important;
    border: none !important;
    border-top: none !important;
    border-bottom: 1px solid var(--border-color) !important;
    border-left: none !important;
    border-right: none !important;
    color: var(--text-primary) !important;
}

/* Mobile log card field */
.mobile-log-field {
    margin-bottom: 8px !important;
    color: var(--text-primary) !important;
}

.mobile-log-field strong {
    color: var(--text-primary) !important;
}

/* Mobile log card detail field with word wrapping */
.mobile-log-detail {
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    color: var(--text-primary) !important;
}

/* Mobile log header styling */
.mobile-log-header {
    position: static !important;
    margin: 0px !important;
    padding: 8px 8px !important;
    border: none !important;
    box-shadow: none !important;
    font-size: 16px !important;
    font-weight: 600 !important;
    line-height: 1.2 !important;
    background-color: transparent !important;
    color: var(--text-primary) !important;
}

/* Mobile log refresh button */
.mobile-log-refresh-btn {
    margin-left: 8px !important;
    padding: 4px !important;
    min-height: 20px !important;
    min-width: 20px !important;
    font-size: 10px !important;
}

/* Mobile log clickable span */
.mobile-log-clickable {
    cursor: pointer !important;
    color: var(--text-secondary) !important;
}

/* Dark theme support for mobile log cards */
[data-theme="dark"] .mobile-log-container {
    background-color: var(--bg-primary) !important;
}

[data-theme="dark"] .mobile-log-card {
    background-color: var(--card-bg) !important;
    border-bottom: 1px solid var(--border-color) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .mobile-log-field {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .mobile-log-field strong {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .mobile-log-detail {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .mobile-log-header {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .mobile-log-clickable {
    color: var(--text-secondary) !important;
}

/* ==============================|| UTILITY CLASSES ||============================== */

/* Clickable cursor */
.cursor-pointer {
    cursor: pointer !important;
}

/* Loading cursor */
.cursor-wait {
    cursor: wait !important;
}

/* Reduced opacity for loading states */
.opacity-60 {
    opacity: 0.6 !important;
}

/* Margin right 8px */
.margin-right-8 {
    margin-right: 8px !important;
}

/* User dropdown styling */
.user-dropdown-content {
    display: flex !important;
    flex-direction: column !important;
}

.user-dropdown-name {
    font-weight: bold !important;
    color: var(--text-primary) !important;
}

.user-dropdown-details {
    font-size: 0.9em !important;
    color: var(--text-secondary) !important;
}

/* Dark theme support for user dropdown */
[data-theme="dark"] .user-dropdown-name {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .user-dropdown-details {
    color: var(--text-secondary) !important;
}

/* ==============================|| TABLE FOOTER FIX ||============================== */

/* Ensure table footer always appears at the bottom */
.ui.table {
    display: table !important;
}

.ui.table thead {
    display: table-header-group !important;
}

.ui.table tbody {
    display: table-row-group !important;
}

.ui.table tfoot {
    display: table-footer-group !important;
}

/* Semantic UI uses Table.Footer which creates tfoot */
.ui.table > tfoot,
.ui.table > .ui.table.footer {
    display: table-footer-group !important;
}

/* Icon-only header buttons on medium screens */
@media screen and (min-width: 768px) and (max-width: 1399px) {
    .ui.menu .item {
        padding-left: 0.6em !important;
        padding-right: 0.6em !important;
        min-width: auto !important;
    }

    /* Ensure icons are properly centered in icon-only mode */
    .ui.menu .item>.icon:only-child {
        margin: 0 !important;
    }

    /* For items with only icons, reduce padding further */
    .ui.menu .item[title] {
        padding-left: 0.5em !important;
        padding-right: 0.5em !important;
    }
}

/* Prevent header overflow and ensure proper layout */
.ui.menu {
    overflow: visible !important;
}

.ui.menu .ui.container {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
}

/* Ensure right menu stays on the right */
.ui.menu .menu.right {
    margin-left: auto !important;
    display: flex !important;
    align-items: center !important;
}

/* Fix center alignment issue - ensure proper left-right balance */
.ui.menu .ui.container {
    width: 100% !important;
}

/* Ensure navigation buttons stay left-aligned */
.ui.menu .ui.container>.item:not(.right) {
    margin-right: 0 !important;
}

/* Force proper layout on all screen sizes */
@media screen and (min-width: 1400px) {
    .ui.menu .ui.container {
        justify-content: space-between !important;
    }
}

/* ==============================|| MOBILE-FIRST RESPONSIVE DESIGN ||============================== */

/* Base mobile styles (0-768px) */
@media screen and (max-width: 768px) {

    /* Ensure proper body spacing for mobile */
    body {
        padding-top: 60px !important;
        /* Maintain header space */
    }

    /* Ensure navigation menu is properly positioned */
    .ui.menu {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        z-index: 1000 !important;
        height: 60px !important;
    }

    /* Mobile sidebar menu styling - Fix overlapping text issue and gaps */
    .ui.segment {
        background-color: var(--card-bg) !important;
        border: 1px solid var(--border-color) !important;
        box-shadow: 0 2px 8px var(--shadow-color) !important;
        margin: 0 !important;
        padding: 0 !important;
        border-radius: 0 !important;
    }

    .ui.menu.secondary.vertical {
        background-color: var(--card-bg) !important;
        border: none !important;
        box-shadow: none !important;
        margin: 0 !important;
        padding: 0 !important;
        border-radius: 0 !important;
    }

    .ui.menu.secondary.vertical .item {
        background-color: var(--card-bg) !important;
        color: var(--text-primary) !important;
        border-bottom: 1px solid var(--border-color) !important;
        padding: 12px 16px !important;
        margin: 0 !important;
        transition: all 0.2s ease !important;
        border-radius: 0 !important;
        border-left: none !important;
        border-right: none !important;
        border-top: none !important;
    }

    .ui.menu.secondary.vertical .item:hover {
        background-color: var(--menu-hover) !important;
        color: var(--text-primary) !important;
    }

    .ui.menu.secondary.vertical .item:last-child {
        border-bottom: none !important;
    }

    /* Fix dropdown items within mobile sidebar to remove gaps */
    .ui.menu.secondary.vertical .item .ui.dropdown {
        background-color: var(--card-bg) !important;
        border: 1px solid var(--border-color) !important;
        margin: 0 !important;
        border-radius: 4px !important;
    }

    .ui.menu.secondary.vertical .item .ui.dropdown .menu {
        background-color: var(--card-bg) !important;
        border: 1px solid var(--border-color) !important;
        box-shadow: 0 4px 12px var(--shadow-color) !important;
        margin: 0 !important;
        border-radius: 4px !important;
    }

    .ui.menu.secondary.vertical .item .ui.dropdown .menu .item {
        background-color: var(--card-bg) !important;
        color: var(--text-primary) !important;
        border-bottom: 1px solid var(--border-color) !important;
        padding: 8px 12px !important;
        margin: 0 !important;
        border-radius: 0 !important;
    }

    .ui.menu.secondary.vertical .item .ui.dropdown .menu .item:hover {
        background-color: var(--menu-hover) !important;
    }

    /* Ensure buttons in mobile sidebar have proper styling */
    .ui.menu.secondary.vertical .item .ui.button {
        background-color: transparent !important;
        color: var(--text-primary) !important;
        border: none !important;
        padding: 8px 12px !important;
        font-size: 14px !important;
    }

    .ui.menu.secondary.vertical .item .ui.button:hover {
        background-color: var(--menu-hover) !important;
        color: var(--text-primary) !important;
    }

    /* Dark theme mobile sidebar styling - ensure no gaps */
    [data-theme="dark"] .ui.segment {
        background-color: var(--card-bg) !important;
        border: 1px solid var(--border-color) !important;
        color: var(--text-primary) !important;
        margin: 0 !important;
        padding: 0 !important;
        border-radius: 0 !important;
    }

    [data-theme="dark"] .ui.menu.secondary.vertical {
        background-color: var(--card-bg) !important;
        margin: 0 !important;
        padding: 0 !important;
        border-radius: 0 !important;
    }

    [data-theme="dark"] .ui.menu.secondary.vertical .item {
        background-color: var(--card-bg) !important;
        color: var(--text-primary) !important;
        border-bottom: 1px solid var(--border-color) !important;
        margin: 0 !important;
        border-radius: 0 !important;
        border-left: none !important;
        border-right: none !important;
        border-top: none !important;
    }

    [data-theme="dark"] .ui.menu.secondary.vertical .item:hover {
        background-color: var(--menu-hover) !important;
        color: var(--text-primary) !important;
    }

    /* Dark theme dropdown styling in mobile sidebar */
    [data-theme="dark"] .ui.menu.secondary.vertical .item .ui.dropdown {
        background-color: var(--card-bg) !important;
        border: 1px solid var(--border-color) !important;
        color: var(--text-primary) !important;
    }

    [data-theme="dark"] .ui.menu.secondary.vertical .item .ui.dropdown .menu {
        background-color: var(--card-bg) !important;
        border: 1px solid var(--border-color) !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
    }

    [data-theme="dark"] .ui.menu.secondary.vertical .item .ui.dropdown .menu .item {
        background-color: var(--card-bg) !important;
        color: var(--text-primary) !important;
        border-bottom: 1px solid var(--border-color) !important;
    }

    [data-theme="dark"] .ui.menu.secondary.vertical .item .ui.dropdown .menu .item:hover {
        background-color: var(--menu-hover) !important;
        color: var(--text-primary) !important;
    }

    [data-theme="dark"] .ui.menu.secondary.vertical .item .ui.button {
        background-color: transparent !important;
        color: var(--text-primary) !important;
        border: none !important;
    }

    [data-theme="dark"] .ui.menu.secondary.vertical .item .ui.button:hover {
        background-color: var(--menu-hover) !important;
        color: var(--text-primary) !important;
    }

    /* Reset containers for mobile - NO padding */
    .ui.container {
        width: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
        /* Remove all padding */
        max-width: none !important;
    }

    /* Main content with minimal padding */
    .main-content {
        padding: 4px !important;
        /* Minimal padding */
    }

    /* Dashboard container with minimal padding */
    .dashboard-container {
        padding: 4px !important;
        /* Minimal padding */
    }

    /* Card styling - minimal margins, no borders */
    .ui.card,
    .ui.cards>.card {
        margin: 2px 0 !important;
        /* Minimal vertical margin only */
        border: none !important;
        /* Remove borders */
        border-radius: 4px !important;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
    }

    /* Segment styling - minimal margins, no borders */
    .ui.segment {
        margin: 2px 0 !important;
        /* Minimal vertical margin only */
        border: none !important;
        /* Remove borders */
        border-radius: 4px !important;
        box-shadow: none !important;
    }

    /* Table styling - no borders */
    .ui.table {
        border: none !important;
        /* Remove borders */
        border-radius: 4px !important;
        margin: 2px 0 !important;
        /* Minimal vertical margin only */
        box-shadow: none !important;
    }

    /* Remove all horizontal margins and padding from common elements */
    .ui.segment,
    .ui.card,
    .ui.form,
    .ui.table,
    .ui.container {
        margin-left: 0 !important;
        margin-right: 0 !important;
        padding-left: 0 !important;
        padding-right: 0 !important;
    }

    /* Form styling - remove all borders */
    .ui.form {
        margin: 0 !important;
        border: none !important;
    }

    .ui.form .ui.segment {
        border: none !important;
        box-shadow: none !important;
        padding: 4px !important;
        /* Reduced padding */
        margin: 0 !important;
        background: var(--bg-primary) !important;
    }

    /* Ensure form fields take full width with minimal padding */
    .ui.form .field,
    .ui.form .fields {
        margin-left: 0 !important;
        margin-right: 0 !important;
        padding-left: 4px !important;
        padding-right: 4px !important;
    }

    .ui.form .field {
        margin-bottom: 0.75rem !important;
    }

    /* Table responsive design - convert to mobile layout */
    .ui.table {
        border: none !important;
        box-shadow: none !important;
        margin: 0 !important;
        background: var(--bg-primary) !important;
    }

    .ui.table thead {
        display: none !important;
        /* Hide table headers on mobile */
    }

    .ui.table tbody tr {
        display: block !important;
        border: none !important;
        border-radius: 0 !important;
        margin-bottom: 8px !important;
        padding: 12px !important;
        background: var(--bg-primary) !important;
        box-shadow: none !important;
    }

    .ui.table tbody tr td {
        display: block !important;
        border: none !important;
        padding: 4px 0 !important;
        text-align: left !important;
        position: relative !important;
        padding-left: 40% !important;
    }

    .ui.table tbody tr td:before {
        content: attr(data-label) !important;
        position: absolute !important;
        left: 0 !important;
        width: 35% !important;
        font-weight: bold !important;
        color: var(--text-secondary) !important;
        font-size: 0.9em !important;
    }

    /* Button groups - stack vertically */
    .ui.buttons {
        flex-direction: column !important;
    }

    .ui.buttons .button {
        margin: 2px 0 !important;
        width: 100% !important;
    }

    /* Menu - full width */
    .ui.menu {
        border: none !important;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    }

    .ui.menu .ui.container {
        padding: 0 8px !important;
    }

    /* Hide elements that take too much space on mobile */
    .hide-on-mobile {
        display: none !important;
    }

    /* Compact statistics */
    .ui.statistic {
        margin: 0.5em 0 !important;
    }

    .ui.statistic>.value {
        font-size: 1.5rem !important;
    }

    /* Modal adjustments */
    .ui.modal {
        margin: 8px !important;
        width: calc(100% - 16px) !important;
    }
}

/* ==============================|| TABLET RESPONSIVE DESIGN ||============================== */

/* Tablet styles (769px - 1366px) */
@media screen and (min-width: 769px) and (max-width: 1366px) {
    .ui.container {
        width: auto !important;
        max-width: 100% !important;
        margin-left: auto !important;
        margin-right: auto !important;
        padding: 0 16px !important;
    }

    /* Reduce card margins and borders for tablets */
    .ui.card,
    .ui.cards>.card {
        border-radius: 4px !important;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
    }

    /* Table adjustments for tablets */
    .ui.table {
        font-size: 0.9em;
        border-radius: 4px !important;
    }

    .ui.table th,
    .ui.table td {
        padding: 8px 6px !important;
    }

    /* Card layout optimization */
    .ui.cards {
        margin-left: -0.5em !important;
        margin-right: -0.5em !important;
    }

    .ui.cards>.card {
        margin: 0.5em !important;
        width: calc(50% - 1em) !important;
    }

    /* Content area adjustments */
    .main-content {
        padding: 12px !important;
    }

    .dashboard-container {
        padding: 1rem !important;
        max-width: none !important;
    }

    /* Form adjustments */
    .ui.form .ui.segment {
        padding: 16px !important;
    }

    /* Menu adjustments */
    .ui.menu .ui.container {
        padding: 0 16px !important;
    }
}

/* ==============================|| DESKTOP RESPONSIVE DESIGN ||============================== */

/* Desktop styles (1367px+) */
@media screen and (min-width: 1367px) {
    .ui.container {
        width: 1200px !important;
        margin-left: auto !important;
        margin-right: auto !important;
        padding: 0 !important;
    }

    /* Restore full styling for desktop */
    .ui.card,
    .ui.cards>.card {
        border-radius: 8px !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    }

    .ui.table {
        border-radius: 8px !important;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
    }

    .main-content {
        padding: 16px !important;
    }

    .dashboard-container {
        padding: 20px 24px 40px !important;
        max-width: 1600px !important;
    }
}

/* 优化 Dashboard 网格布局 */
@media screen and (max-width: 1366px) {
    .charts-grid {
        margin: 0 -0.5em !important;
    }

    .charts-grid .column {
        padding: 0.5em !important;
    }

    .chart-card {
        margin: 0 !important;
    }

    /* 调整字体大小 */
    .ui.header {
        font-size: 1.1em !important;
    }

    .stat-value {
        font-size: 0.9em !important;
    }
}
